from __future__ import annotations

import json
import threading
import typing as t
from dataclasses import dataclass, field
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import logging
import time

logger = logging.getLogger(__name__)

@dataclass
class EditorData:
    """编辑器数据结构"""
    content: str = ""
    front_matter: dict[str, t.Any] = field(default_factory=dict)
    last_updated: float = field(default_factory=time.time)

class HTTPAPIServer:
    """
    HTTP API 服务器，用于 Rio 应用与 Vditor 编辑器之间的通信
    """
    
    def __init__(self, host: str = "localhost", port: int = 8765):
        self.host = host
        self.port = port
        self.server = None
        self.editor_data = EditorData()
        self._server_thread = None
        self._is_running = False
        
        # 回调函数
        self.on_content_change: t.Callable[[str], None] = lambda x: None
        self.on_save_request: t.Callable[[str], None] = lambda x: None
        self.on_front_matter_change: t.Callable[[dict], None] = lambda x: None
        
    def start_server(self):
        """启动 HTTP API 服务器"""
        class APIRequestHandler(BaseHTTPRequestHandler):
            api_server = self
            
            def do_OPTIONS(self):
                """处理 CORS 预检请求"""
                self.send_response(200)
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                self.end_headers()
            
            def do_GET(self):
                """处理 GET 请求"""
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                parsed_path = urlparse(self.path)
                path = parsed_path.path
                
                if path == '/api/content':
                    # 获取内容
                    response = {
                        'content': self.api_server.editor_data.content
                    }
                elif path == '/api/front-matter':
                    # 获取 front matter
                    response = {
                        'front_matter': self.api_server.editor_data.front_matter
                    }
                elif path == '/api/health':
                    # 健康检查
                    response = {
                        'status': 'ok'
                    }
                else:
                    # 未知路径
                    response = {
                        'success': False,
                        'message': 'Unknown endpoint'
                    }
                
                self.wfile.write(json.dumps(response).encode('utf-8'))
            
            def do_POST(self):
                """处理 POST 请求"""
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                parsed_path = urlparse(self.path)
                path = parsed_path.path
                
                # 获取请求体
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8'))
                    
                    if path == '/api/content':
                        # 更新内容
                        content = data.get('content', '')
                        self.api_server.editor_data.content = content
                        self.api_server.editor_data.last_updated = time.time()
                        
                        # 触发回调
                        self.api_server.on_content_change(content)
                        
                        response = {
                            'success': True,
                            'message': 'Content updated successfully'
                        }
                    elif path == '/api/save':
                        # 保存请求
                        content = data.get('content', '')
                        front_matter = data.get('front_matter', {})
                        self.api_server.editor_data.content = content
                        self.api_server.editor_data.front_matter = front_matter
                        self.api_server.editor_data.last_updated = time.time()
                        
                        # 触发保存回调
                        self.api_server.on_save_request(content)
                        
                        response = {
                            'success': True,
                            'message': 'Content saved successfully'
                        }
                    elif path == '/api/front-matter':
                        # 更新 Front Matter
                        front_matter = data.get('front_matter', {})
                        self.api_server.editor_data.front_matter = front_matter
                        self.api_server.editor_data.last_updated = time.time()
                        
                        # 触发回调
                        self.api_server.on_front_matter_change(front_matter)
                        
                        response = {
                            'success': True,
                            'message': 'Front matter updated successfully'
                        }
                    else:
                        # 未知路径
                        response = {
                            'success': False,
                            'message': 'Unknown endpoint'
                        }
                    
                    self.wfile.write(json.dumps(response).encode('utf-8'))
                    
                except json.JSONDecodeError:
                    error_response = {
                        'success': False,
                        'message': 'Invalid JSON data'
                    }
                    self.wfile.write(json.dumps(error_response).encode('utf-8'))
                except Exception as e:
                    error_response = {
                        'success': False,
                        'message': f'Error processing request: {str(e)}'
                    }
                    self.wfile.write(json.dumps(error_response).encode('utf-8'))
            
            def log_message(self, format, *args):
                """自定义日志消息"""
                logger.info(format % args)
        
        try:
            self.server = HTTPServer((self.host, self.port), APIRequestHandler)
            self._is_running = True
            logger.info(f"HTTP API 服务器已启动: http://{self.host}:{self.port}")
        except Exception as e:
            logger.error(f"启动服务器失败: {e}")
            raise
    
    def start_in_thread(self):
        """在新线程中启动服务器"""
        self._server_thread = threading.Thread(target=self._run_server, daemon=True)
        self._server_thread.start()
    
    def _run_server(self):
        """运行服务器"""
        self.start_server()
        while self._is_running:
            self.server.handle_request()
    
    def stop_server(self):
        """停止 HTTP API 服务器"""
        self._is_running = False
        if self.server:
            self.server.server_close()
            logger.info("HTTP API 服务器已停止")
    
    def get_content(self) -> str:
        """获取当前内容"""
        return self.editor_data.content
    
    def get_front_matter(self) -> dict:
        """获取当前 Front Matter"""
        return self.editor_data.front_matter
    
    def start(self):
        """启动 HTTP API 服务器"""
        self.start_in_thread()
    
    def stop(self):
        """停止 HTTP API 服务器"""
        self.stop_server()
    
    def set_content_callback(self, callback: t.Callable[[str], None]):
        """设置内容变化回调函数"""
        self.on_content_change = callback
    
    def set_front_matter_callback(self, callback: t.Callable[[dict], None]):
        """设置 front matter 变化回调函数"""
        self.on_front_matter_change = callback
    
    def set_content(self, content: str):
        """设置内容"""
        self.editor_data.content = content
        self.editor_data.last_updated = time.time()
    
    def set_front_matter(self, front_matter: dict):
        """设置 front matter"""
        self.editor_data.front_matter = front_matter
        self.editor_data.last_updated = time.time()