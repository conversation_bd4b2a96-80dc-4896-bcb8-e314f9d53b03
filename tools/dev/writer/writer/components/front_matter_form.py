from __future__ import annotations

from dataclasses import KW_ONLY, field
import typing as t
import datetime

import rio

class FrontMatterForm(rio.Component):
    """
    Front Matters 设置表单组件，用于创建和编辑 Hugo 文章的 Front Matters。
    """
    
    # 表单字段
    title: str = ""
    date: datetime.datetime = field(default_factory=datetime.datetime.now)
    draft: bool = False
    author: str = "spixed"
    keywords: str = ""
    categories: str = ""
    tags: str = ""
    noclick: bool = False
    status: bool = False
    statusCate: str = ""
    categoryLink: str = ""
    buy: bool = False
    buyLink: str = ""
    buyName: str = ""
    buyInfo: str = ""
    buyImage: str = ""
    buyButtonText: str = "Click"
    weight: int = 1
    
    # 回调函数
    on_submit: t.Callable[[FrontMatterForm], None] = lambda x: None
    
    def build(self) -> rio.Component:
        return rio.Column(
            rio.Text("Front Matters 设置", style="heading1"),
            
            # 基本信息
            rio.Text("基本信息", style="heading3"),
            rio.TextInput(
                label="标题",
                text=self.title,
                on_change=self._on_title_change,
            ),
            rio.Text(
                f"日期: {self.date.strftime('%Y-%m-%d %H:%M:%S')}",
                style="text",
            ),
            rio.Row(
                rio.Text("草稿"),
                rio.Switch(
                    is_on=self.draft,
                    on_change=self._on_draft_change,
                ),
                spacing=1,
            ),
            rio.TextInput(
                label="作者",
                text=self.author,
                on_change=self._on_author_change,
            ),
            
            # 分类和标签
            rio.Text("分类和标签", style="heading3"),
            rio.TextInput(
                label="关键词（用逗号分隔）",
                text=self.keywords,
                on_change=self._on_keywords_change,
            ),
            rio.TextInput(
                label="分类（用逗号分隔）",
                text=self.categories,
                on_change=self._on_categories_change,
            ),
            rio.TextInput(
                label="标签（用逗号分隔）",
                text=self.tags,
                on_change=self._on_tags_change,
            ),
            
            # 显示设置
            rio.Text("显示设置", style="heading3"),
            rio.Row(
                rio.Text("禁止点击"),
                rio.Switch(
                    is_on=self.noclick,
                    on_change=self._on_noclick_change,
                ),
                spacing=1,
            ),
            rio.Row(
                rio.Text("状态模式"),
                rio.Switch(
                    is_on=self.status,
                    on_change=self._on_status_change,
                ),
                spacing=1,
            ),
            rio.TextInput(
                label="状态分类",
                text=self.statusCate,
                on_change=self._on_statusCate_change,
            ),
            rio.TextInput(
                label="分类链接",
                text=self.categoryLink,
                on_change=self._on_categoryLink_change,
            ),
            
            # 购买设置
            rio.Text("购买设置", style="heading3"),
            rio.Row(
                rio.Text("启用购买"),
                rio.Switch(
                    is_on=self.buy,
                    on_change=self._on_buy_change,
                ),
                spacing=1,
            ),
            rio.TextInput(
                label="购买链接",
                text=self.buyLink,
                on_change=self._on_buyLink_change,
            ),
            rio.TextInput(
                label="购买名称",
                text=self.buyName,
                on_change=self._on_buyName_change,
            ),
            rio.TextInput(
                label="购买信息",
                text=self.buyInfo,
                on_change=self._on_buyInfo_change,
            ),
            rio.TextInput(
                label="购买图片",
                text=self.buyImage,
                on_change=self._on_buyImage_change,
            ),
            rio.TextInput(
                label="购买按钮文本",
                text=self.buyButtonText,
                on_change=self._on_buyButtonText_change,
            ),
            
            # 其他设置
            rio.Text("其他设置", style="heading3"),
            rio.NumberInput(
                label="权重",
                value=self.weight,
                on_change=self._on_weight_change,
            ),
            
            # 提交按钮
            rio.Button(
                "保存并继续编辑内容",
                on_press=self._on_submit,
                style="major",
            ),
            
            spacing=1,
            margin=2,
        )


    # 事件处理函数
    def _on_title_change(self, event: rio.TextInputChangeEvent) -> None:
        self.title = event.text
    
    def _on_draft_change(self, event: rio.SwitchChangeEvent) -> None:
        self.draft = event.is_on
    
    def _on_author_change(self, event: rio.TextInputChangeEvent) -> None:
        self.author = event.text
    
    def _on_keywords_change(self, event: rio.TextInputChangeEvent) -> None:
        self.keywords = event.text
    
    def _on_categories_change(self, event: rio.TextInputChangeEvent) -> None:
        self.categories = event.text
    
    def _on_tags_change(self, event: rio.TextInputChangeEvent) -> None:
        self.tags = event.text
    
    def _on_noclick_change(self, event: rio.SwitchChangeEvent) -> None:
        self.noclick = event.is_on
    
    def _on_status_change(self, event: rio.SwitchChangeEvent) -> None:
        self.status = event.is_on
    
    def _on_statusCate_change(self, event: rio.TextInputChangeEvent) -> None:
        self.statusCate = event.text
    
    def _on_categoryLink_change(self, event: rio.TextInputChangeEvent) -> None:
        self.categoryLink = event.text
    
    def _on_buy_change(self, event: rio.SwitchChangeEvent) -> None:
        self.buy = event.is_on
    
    def _on_buyLink_change(self, event: rio.TextInputChangeEvent) -> None:
        self.buyLink = event.text
    
    def _on_buyName_change(self, event: rio.TextInputChangeEvent) -> None:
        self.buyName = event.text
    
    def _on_buyInfo_change(self, event: rio.TextInputChangeEvent) -> None:
        self.buyInfo = event.text
    
    def _on_buyImage_change(self, event: rio.TextInputChangeEvent) -> None:
        self.buyImage = event.text
    
    def _on_buyButtonText_change(self, event: rio.TextInputChangeEvent) -> None:
        self.buyButtonText = event.text
    
    def _on_weight_change(self, event: rio.NumberInputChangeEvent) -> None:
        self.weight = int(event.value)
    
    def _on_submit(self) -> None:
        self.on_submit(self)
