from __future__ import annotations

from dataclasses import KW_ONLY, field
import typing as t
from pathlib import Path

import rio

from .http_api_server import HTTPAPIServer

class ContentEditor(rio.Component):
    """
    内容编辑器组件，使用本地文件系统与 Vditor 编辑器进行通信。
    """

    # Front Matters 数据
    front_matter: dict[str, t.Any] = field(default_factory=dict)

    # 文章内容
    content: str = ""

    # 回调函数
    on_save: t.Callable[[str, dict[str, t.Any]], None] = lambda content, front_matter: None
    on_back: t.Callable[[], None] = lambda: None

    # HTTP API 服务器
    _api_server: HTTPAPIServer = field(default=None, init=False)
    
    def __post_init__(self):
        """组件初始化后的设置"""
        self._setup_api_server()

    def _setup_api_server(self):
        """设置 HTTP API 服务器"""
        self._api_server = HTTPAPIServer()
        self._api_server.set_content_callback(self._on_content_changed)
        self._api_server.set_front_matter_callback(self._on_front_matter_changed)
        self._api_server.start()
    
    def _on_content_changed(self, content):
        """当内容变化时调用"""
        self.content = content
        if self.on_save:
            self.on_save(self.content, self.front_matter)
    
    def _on_front_matter_changed(self, front_matter):
        """当 front matter 变化时调用"""
        self.front_matter = front_matter
        if self.on_save:
            self.on_save(self.content, self.front_matter)
    
    def update_content(self, content):
        """更新编辑器中的内容"""
        self.content = content
        if self._api_server:
            self._api_server.set_content(content)
    
    def update_front_matter(self, front_matter):
        """更新编辑器中的 front matter"""
        self.front_matter = front_matter
        if self._api_server:
            self._api_server.set_front_matter(front_matter)

    def build(self) -> rio.Component:
        # 获取 assets/index.html 文件的路径
        assets_dir = Path(__file__).parent.parent / "assets"
        index_html_path = assets_dir / "index.html"
        
        return rio.Column(
            rio.Row(
                rio.Button(
                    "返回 Front Matters 设置",
                    on_press=self._on_back,
                    style="minor",
                ),
                rio.Button(
                    "保存文章",
                    on_press=self._on_save,
                    style="major",
                ),
                spacing=1,
                margin_bottom=1,
            ),
            
            rio.Webview(
                # 使用现有的 assets/index.html 文件
                content=str(index_html_path),
                grow_y=True,
                min_height=600,
            ),
            
            spacing=1,
            margin=2,
        )
    
    def _on_back(self) -> None:
        self.on_back()
    
    def _on_save(self) -> None:
        # 从 HTTP API 服务器获取最新内容
        if self._api_server:
            self.content = self._api_server.get_content()
            self.front_matter = self._api_server.get_front_matter()
        self.on_save(self.content, self.front_matter)


    def __del__(self):
        """清理资源"""
        self._stop_monitoring = True
        if self._api_server:
            self._api_server.stop()
        if self._temp_dir and self._temp_dir.exists():
            import shutil
            shutil.rmtree(self._temp_dir, ignore_errors=True)
